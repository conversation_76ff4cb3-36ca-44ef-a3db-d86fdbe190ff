package team.aikero.murmuration.controller.web

import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.core.workflow.dto.WorkflowDefinitionPageReq
import team.aikero.murmuration.core.workflow.dto.WorkflowDefinitionPageVo
import team.aikero.murmuration.core.workflow.entity.NodeDefinition
import team.aikero.murmuration.core.workflow.entity.WorkflowDefinition

/**
 * 工作流定义
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/workflow/definition")
class WorkflowDefinitionController(val sqlClient: KSqlClient) {

    /**
     * 创建工作流定义
     *
     * @param workflowDefinition 工作流定义
     */
    @PostMapping("/create")
    @Transactional(rollbackFor = [Exception::class])
    fun create(@RequestBody workflowDefinition: WorkflowDefinition): DataResponse<Long> {
        val result = sqlClient.save(workflowDefinition) {
            setKeyOnlyAsReference(NodeDefinition::nodeMetadata)
        }
        return ok(result.modifiedEntity.id)
    }

    /**
     * 更新工作流定义
     *
     * @param workflowDefinition 工作流定义
     */
    @PostMapping("/update")
    @Transactional(rollbackFor = [Exception::class])
    fun update(@RequestBody workflowDefinition: WorkflowDefinition): DataResponse<Unit> {

        sqlClient.save(workflowDefinition) {
            setAssociatedModeAll(AssociatedSaveMode.VIOLENTLY_REPLACE)
            setKeyOnlyAsReference(NodeDefinition::nodeMetadata)
        }
        return ok()
    }

    /**
     * 工作流定义列表
     *
     * @param pageReq 分页请求
     */
    @PostMapping("/list")
    fun list(@RequestBody pageReq: WorkflowDefinitionPageReq): DataResponse<List<WorkflowDefinitionPageVo>> {
        val pageVos = sqlClient.executeQuery(WorkflowDefinition::class) {
            where(pageReq)
            select(table.fetch(WorkflowDefinitionPageVo::class))
        }

        // 过滤掉非任务节点
        return ok(pageVos)
    }
}
