package team.aikero.murmuration.controller.web

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.web.bind.annotation.*
import team.aikero.blade.auth.annotation.PreCheckPermission
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.findPage
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.dto.WorkflowInstanceDetailVo
import team.aikero.murmuration.core.workflow.dto.WorkflowInstancePageReq
import team.aikero.murmuration.core.workflow.dto.WorkflowInstancePageVo
import team.aikero.murmuration.core.workflow.engine.GraphEngine
import team.aikero.murmuration.core.workflow.entity.WorkflowInstance
import team.aikero.murmuration.core.workflow.entity.deleted
import team.aikero.murmuration.core.workflow.entity.id

/**
 * 工作流实例
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/workflow/instance")
class WorkflowInstanceController(
    val sqlClient: KSqlClient,
    val graphEngine: GraphEngine,

) {

    /**
     * 工作流运行
     *
     * @param workflowRunReq 工作流运行请求
     */
    @PostMapping("/run")
    fun run(@RequestBody workflowRunReq: WorkflowDefinitionRunReq): DataResponse<Long> {

        // 启动工作流
        val workflowInstanceId = graphEngine.startWorkflow(
            workflowDefinitionId = workflowRunReq.workflowDefinitionId,
            input = workflowRunReq.input,
            parameter = workflowRunReq.parameter?: mapOf(),
            nodeArgs = workflowRunReq.nodeArgs
        )

        return ok(workflowInstanceId)
    }

    /**
     * 工作流实例列表
     *
     * @param workflowDefinitionPageReq 工作流实例分页请求
     */
    @PostMapping("/page")
    @PreCheckPermission(name = "POD风格化-工作流实例列表", value = ["murmuration.workflowInstance.page"])
    fun page(@RequestBody workflowDefinitionPageReq: ComplexPageParam<WorkflowInstancePageReq>): DataResponse<PageVo<WorkflowInstancePageVo>> {
        return ok(sqlClient.findPage(workflowDefinitionPageReq, WorkflowInstancePageVo::class))
    }

    /**
     * 工作流实例详情
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @GetMapping("/{workflowInstanceId}/detail")
    @PreCheckPermission(name = "POD风格化-工作流实例详情", value = ["murmuration.workflowInstance.detail"])
    fun detail(@PathVariable workflowInstanceId: Long): DataResponse<WorkflowInstanceDetailVo> {
        val instanceDetailVo = sqlClient.findOneById(WorkflowInstanceDetailVo::class, workflowInstanceId)
        return ok(instanceDetailVo)
    }

    /**
     * 暂停工作流
     *
     * @param workflowInstanceId 工作流实例ID
     *
     */
    @PostMapping("/{workflowInstanceId}/pause")
    fun pause(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.pauseWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 恢复工作流
     *
     * 恢复暂停的工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @PostMapping("/{workflowInstanceId}/resume")
    fun resume(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.resumeWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 取消工作流
     *
     * 中止正在运行中的工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @PostMapping("/{workflowInstanceId}/cancel")
    fun cancel(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.cancelWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 重试工作流
     *
     * 重新执行工作流失败的节点（不会重新发起外部任务）
     *
     * 例如：任务等待超时，进行重试
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @PostMapping("/{workflowInstanceId}/retry")
    fun retry(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.retryWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 重置工作流
     *
     * 重新执行工作流失败的节点，并恢复节点初始状态（可能会重新发起外部任务）
     *
     * 例如：供应商任务创建错误，进行重试
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @PostMapping("/{workflowInstanceId}/reset")
    fun reset(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.resetWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 恢复运行的工作流
     *
     * 重新调度工作流节点
     *
     * 例如：节点的队列任务丢失，进行重投
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @PostMapping("/{workflowInstanceId}/recover")
    fun recover(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {
        graphEngine.recoverWorkflow(workflowInstanceId)
        return ok()
    }

    /**
     * 删除工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    @DeleteMapping("/{workflowInstanceId}/delete")
    fun delete(@PathVariable workflowInstanceId: Long): DataResponse<Unit> {

        graphEngine.cancelWorkflow(workflowInstanceId)

        sqlClient.executeUpdate(WorkflowInstance::class){
            set(table.deleted, true)
            where(table.id eq workflowInstanceId)
        }

        return ok()
    }

}

/**
 * 工作流运行请求
 */
data class WorkflowDefinitionRunReq(
    /**
     * 工作流定义ID
     */
    val workflowDefinitionId: Long,

    /**
     * 工作流输入
     */
    val input: Map<String, Any>,

    /**
     * 工作流参数
     */
    val parameter: Map<String, Any>?,

    /**
     * 节点参数
     */
    val nodeArgs: List<NodeDefinitionArgs>,
)

/**
 * 节点输入参数
 */
data class NodeDefinitionArgs(
    /**
     * 节点标识
     */
    val nodeKey: String,

    /**
     * 供应商
     */
    val supplier: Supplier?,

    /**
     * 节点输入
     */
    val input: Map<String, Any>?,

    /**
     * 节点参数
     */
    val parameter: Map<String, Any>?,
)
