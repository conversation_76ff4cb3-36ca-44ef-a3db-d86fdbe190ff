package team.aikero.murmuration.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import team.aikero.murmuration.core.scheduler.backoff.ExponentialBackoffConfig
import team.aikero.murmuration.core.scheduler.backoff.JitterPolicy
import java.math.BigDecimal
import java.time.Duration

/**
 * 服务配置
 *
 * <AUTHOR>
 */
@RefreshScope
@ConfigurationProperties(prefix = "murmuration")
class MurmurationProperties {

    /**
     * 任务相关配置
     */
    var task = Scheduler(
        queueName = "murmuration-task-queue",
        workerCount = 4,
        backoffConfig = ExponentialBackoffConfig(
            base = BigDecimal("1.6"),
            initialDelay = Duration.ofSeconds(1),
            maxDelay = Duration.ofMinutes(13),
            jitterPolicy = JitterPolicy.EQUAL,
        ),
        maxBackoffTimes = 30,
    )

    /**
     * 节点相关配置
     */
    var node = Scheduler(
        queueName = "murmuration-node-queue",
        workerCount = 4,
        backoffConfig = ExponentialBackoffConfig(
            base = BigDecimal("1.6"),
            initialDelay = Duration.ofSeconds(1),
            maxDelay = Duration.ofMinutes(13),
            jitterPolicy = JitterPolicy.EQUAL,
        ),
        maxBackoffTimes = 30,
    )

    /**
     * 调度器相关配置
     */
    data class Scheduler(
        /**
         * 节点队列名称
         */
        var queueName: String,

        /**
         * worker 数量
         */
        var workerCount: Int,

        /**
         * 退避配置
         */
        var backoffConfig: ExponentialBackoffConfig,

        /**
         * 最大退避次数
         */
        var maxBackoffTimes: Int,
    )
}
