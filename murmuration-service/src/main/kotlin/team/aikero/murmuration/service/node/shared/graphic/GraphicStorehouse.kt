package team.aikero.murmuration.service.node.task.aigc_image

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.GeneratedValue
import org.babyfish.jimmer.sql.Id
import team.aikero.blade.data.jimmer.SnowflakeIdGenerator
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.Creator
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 图案库
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Entity
interface GraphicStorehouse: Creator, CreatedTime, TenantId {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generatorType = SnowflakeIdGenerator::class)
    val imageId: Long

    /**
     * 原图ID
     */
    val referId: Long?

    /**
     * 来源图ID
     */
    val originImage: Long?

    /**
     * 来源类型
     */
    val originType: GraphicStorehouseType

    /**
     * 图片地址
     */
    val imageUrl: String

    /**
     * 图片类型
     */
    val imageType: GraphicStorehouseType

    /**
     * 图片格式
     */
    val imageFormat: String
}

enum class GraphicStorehouseType(
    val code: String,
    val value: String
) {
    RESULT_IMAGE("2000", "AI衍生图"),
    UPLOAD_IMAGE("3000", "用户上传图"),
    LOGO_IDENTIFY_IMAGE("5000", "图案提取图"),//原 素材提取图,
    FLORAL_PRINT_EXTRACTION("6000", "花型提取图"),
    EXTERNAL_IMPORT("7000", "外部导入图"),

    ORIGINAL_IMAGE("1000", "原图"),
    TRANSPARENT_IMAGE("4000", "透明图"),
    TRANSPARENT_SEG_IMAGE("4100", "透明分割图"),
    ORIGINAL_TRANSPARENT_SEG_IMAGE("4101", "透明分割原图"),
    ENLARGE_IMAGE("4200", "无损放大2K图"),
    ENLARGE_4K_IMAGE("4210", "无损放大4K图"),
}
