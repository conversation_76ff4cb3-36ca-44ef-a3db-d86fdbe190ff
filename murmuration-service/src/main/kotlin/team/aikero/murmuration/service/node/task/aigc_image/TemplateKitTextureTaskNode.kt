package team.aikero.murmuration.service.node.task.aigc_image

import jakarta.validation.constraints.Size
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.murmuration.common.req.task.TextureRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.*
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import java.math.BigDecimal

/**
 * 版型图套贴图任务节点
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@NodeIdentifier(name = "版型图套贴图", supplier = Supplier.AIGC_IMAGE, ability = Ability.TEXTURE)
class TemplateKitTextureTaskNode: SmartGraphicStorehouseTaskNode<TemplateKitTextureInput, Parameter.Empty, TemplateKitTextureOutput>() {

    data class Pair(val imageId: Long, val templateDetailId: Long)

    @Transactional(rollbackFor = [Exception::class])
    override fun createTask(context: WorkflowNodeContext, input: TemplateKitTextureInput, parameter: Parameter.Empty) {
        val templateDetails = sql.findByIds(FloralPrintTemplateDetail::class, input.templateDetailIds)
        templateDetails.forEach(::validateTemplateDetail)

        // 推送图案库
        input.images


        // 循环创建贴图任务
        for (image in images) {
            // 图套下的每个颜色都要贴一次
            for (templateDetail in templateDetails) {
                // 蒙版图信息
                val (fileUrl, offsetAngle) = templateDetail.maskImages!!.first()
                val rotationAngle = if (offsetAngle.isNullOrEmpty()) null else BigDecimal(offsetAngle)

                // 创建贴图任务
                taskManager.createTask(
                    nodeInstanceId = context.node.id,
                    supplier = Supplier.AIGC_IMAGE,
                    ability = Ability.TEXTURE,
                    request = TextureRequest(
                        foregroundImageUrl = image.imageUrl,
                        backgroundImageUrl = templateDetail.imageUrl,
                        maskImageUrl = fileUrl!!,
                        rotationAngle = rotationAngle,
                    ),
                    // imageId、templateDetailId、taskId 三者要建立关联关系
                    storage = Pair(
                        imageId = image.imageId,
                        templateDetailId = templateDetail.templateDetailId
                    )
                )
            }
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): TemplateKitTextureOutput {
        val storage = taskManager.readTaskStorage<Pair>(context.node.id)
        val images = taskResults.map {
            val (imageId, templateDetailId) = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 款式图
            StyleImage(
                imageUrl = it.url,
                graphicImage = GraphicImage(imageId),
                templateImage = TemplateImage(templateDetailId),
            )
        }

        // 按照图套分组，再聚合
        val kits = images.groupByKit()
        return TemplateKitTextureOutput(kits.flatten())
    }

    private fun validateTemplateDetail(templateDetail: FloralPrintTemplateDetail) {
        if (templateDetail.maskImages.isNullOrEmpty()) {
            throw IllegalStateException("版型明细[${templateDetail.templateDetailId}]缺少蒙版图信息")
        }
    }

}

data class TemplateKitTextureInput(
    /**
     * 图案库图片
     */
    @NodeProperties(name = "图案库图片")
    @field:Size(min = 1)
    val images: List<Image>,
    /**
     * 版型明细ID列表
     */
    @NodeProperties(name = "版型明细")
    @field:Size(min = 1)
    val templateDetailIds: List<Long>,
): Input

data class TemplateKitTextureOutput(
    /**
     * 款式图
     */
    @NodeProperties(name = "款式图")
    val images: List<StyleImage>,
): Output
