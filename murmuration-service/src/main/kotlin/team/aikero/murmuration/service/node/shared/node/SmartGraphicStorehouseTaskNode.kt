package team.aikero.murmuration.service.node.shared.node

import cn.hutool.core.io.FileUtil
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.beans.factory.annotation.Autowired
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.*
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouse
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouseType

/**
 * 支持智能图案库持久化的任务节点基类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
abstract class SmartGraphicStorehouseTaskNode<I : Input, P : Parameter, O : Output> : TaskNode<I, P, O>() {

    @Autowired
    lateinit var sql: KSqlClient


    fun List<Image>.toGraphicImage(): List<GraphicImage> {

    }

    /**
     * 根据节点类型和上下文自动处理引用关系
     */
    fun autoConvertImage(
        originImage: Image?,
        imageUrl: String,
        targetType: GraphicStorehouseType,
    ): GraphicStorehouse {

        // 自动检测原类型
        val originGraphicImage = when (originImage) {
            is GraphicImage -> originImage.image
            is SimpleImage,
            is StyleImage,
            is TemplateImage,
            null -> null
        }

        val storehouse = GraphicStorehouse {
            this.imageUrl = imageUrl
            this.imageType = targetType
            this.originImage = originGraphicImage?.imageId
            this.originType = originGraphicImage?.originType ?: GraphicStorehouseType.ORIGINAL_IMAGE
            this.imageFormat = FileUtil.extName(imageUrl)
            this.tenantId = CurrentUserHolder.get().tenantId

            // 核心逻辑：智能设置referId
            this.referId = calculateReferId(originGraphicImage, targetType)
        }

        return storehouse
    }

    /**
     * 批量存储
     */
    fun List<GraphicStorehouse>.save(): List<GraphicStorehouse> {
        return sql.saveEntities(this).items.map { it.modifiedEntity }
    }

    fun List<SimpleTaskResult>.convertToGraphicStorehouse(
        targetType: GraphicStorehouseType,
        originImage: Image? = null,
    ): List<GraphicStorehouse> {
        return map { autoConvertImage(originImage, it.url, targetType) }
            .save()
    }

    /**
     * 智能计算referId - 实现文档中的复杂逻辑
     */
    private fun calculateReferId(
        originImage: GraphicStorehouse?,
        targetType: GraphicStorehouseType
    ): Long? {

        return when (targetType) {

            // 源头图片类型通常referId为null
            GraphicStorehouseType.ORIGINAL_IMAGE,
            GraphicStorehouseType.UPLOAD_IMAGE,
            GraphicStorehouseType.EXTERNAL_IMPORT -> null

            // 图案提取图特殊处理
            GraphicStorehouseType.LOGO_IDENTIFY_IMAGE -> null

            // 其他衍生图片继承父图referId
            else -> originImage?.referId ?: originImage?.imageId
        }
    }

    /**
     * 从上下文提取输入图片信息
     */
    private fun extractInputImages(context: WorkflowNodeContext): List<GraphicStorehouse> {
        return try {
            val imageIds = context.workflowContext.getStorage<List<Long>>(GRAPHIC_IMAGE_IDS)
            sql.findByIds(GraphicStorehouse::class, imageIds)
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 更新上下文信息
     */
    private fun updateContextWithNewImages(
        context: WorkflowNodeContext,
        images: List<GraphicStorehouse>
    ) {
        context.workflowContext.setStorage(GRAPHIC_IMAGE_IDS, images.map { it.imageId })
        context.workflowContext.setStorage(LATEST_IMAGE_TYPE, images.first().imageType)
    }

    companion object {
        /**
         * 输入图片ID列表
         */
        private const val GRAPHIC_IMAGE_IDS = "input_image_ids"

        /**
         * 最新图片类型
         */
        private const val LATEST_IMAGE_TYPE = "latest_image_type"
    }
}
